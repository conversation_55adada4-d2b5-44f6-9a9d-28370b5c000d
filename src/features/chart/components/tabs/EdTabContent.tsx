import React from 'react';
import {
  <PERSON>,
  Grid,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import type { SxProps, Theme } from '@mui/material/styles';
import EAndMLevelSection from '../sections/EAndMLevelSection';
import type { BOChartInfo } from '../../../../types/chartTypes';

import { chartLogger } from '../../../../utils/logger';

interface EdTabContentProps {
  textFieldSx: SxProps<Theme>;
  isEAndMLevelInlineOpen: boolean;
  setIsEAndMLevelInlineOpen: React.Dispatch<React.SetStateAction<boolean>>;
  chartInfo: BOChartInfo;
  isLoading: boolean;
}

const EdTabContent: React.FC<EdTabContentProps> = ({
  textFieldSx,
  isEAndMLevelInlineOpen,
  setIsEAndMLevelInlineOpen,
  chartInfo,
  isLoading,
}) => {
  const { control, setValue } = useFormContext();

  // Use useWatch for better reactivity with nested objects
  const levelCheckboxStates = useWatch({
    control,
    name: 'levelCheckboxStates',
    defaultValue: {}
  });

  // Debug: Log when checkbox states change
  React.useEffect(() => {
    chartLogger.debug('EdTabContent checkbox states updated', { levelCheckboxStates });
  }, [levelCheckboxStates]);

  // State for E&M Level Accordion expansion
  const [isEAndMLevelSectionExpanded, setIsEAndMLevelSectionExpanded] = React.useState(true);

  const handleEAndMLevelAccordionChange = (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setIsEAndMLevelSectionExpanded(isExpanded);
  };

  const handleEAndMSelectionChange = (itemId: string, checked: boolean) => {
    chartLogger.debug('EdTabContent - Handling E&M selection change', {
      itemId,
      checked,
      currentStates: levelCheckboxStates
    });
    setValue(`levelCheckboxStates.${itemId}`, checked, { shouldDirty: true });
  };

  const handleToggleEAndMLevelInline = () => {
    setIsEAndMLevelInlineOpen(prev => !prev);
  };

  return (
    <Paper 
      elevation={0} 
      sx={{
        paddingLeft: theme => theme.spacing(1.5),
        paddingRight: theme => theme.spacing(1.5),
        paddingBottom: theme => theme.spacing(1.5),
        paddingTop: theme => theme.spacing(1.5),
        backgroundColor: 'background.default', 
        flexGrow: 1, 
        overflowY: 'auto'
      }}
    >
      <Grid container spacing={1.5} alignItems="flex-start" sx={{mb: 1.5}}>
        <Grid item xs={12} sm={6} md={3}>
          <Controller
            name="treatmentArea"
            control={control}
            render={({ field }) => (
              <TextField 
                {...field}
                label="Treatment Area" 
                variant="outlined" 
                size="small" 
                fullWidth
                sx={textFieldSx} 
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Controller
            name="edChartStatus"
            control={control}
            render={({ field }) => (
              <TextField 
                {...field}
                label="ED Chart Status" 
                variant="outlined" 
                size="small" 
                fullWidth
                sx={textFieldSx} 
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name="note"
            control={control}
            render={({ field }) => (
              <TextField 
                {...field}
                label="Note" 
                variant="outlined" 
                size="small" 
                fullWidth
                multiline 
                rows={1}
                sx={textFieldSx} 
              />
            )}
          />
        </Grid>
      </Grid>

      <EAndMLevelSection
        isExpanded={isEAndMLevelSectionExpanded}
        onAccordionChange={handleEAndMLevelAccordionChange}
        levelCheckboxStates={levelCheckboxStates}
        handleEAndMSelectionChange={handleEAndMSelectionChange}
        textFieldSx={textFieldSx}
        isEAndMLevelInlineOpen={isEAndMLevelInlineOpen}
        handleToggleEAndMLevelInline={handleToggleEAndMLevelInline}
        chartInfo={chartInfo}
        isLoading={isLoading}
      />

      <Accordion expanded={true} sx={{ '&.MuiAccordion-root:before': { display: 'none' }, boxShadow: 'none', border: '1px solid', borderColor: 'divider', mb: 1.5 }}>
        <AccordionSummary 
          expandIcon={<ExpandMoreIcon />} 
          aria-controls="medication-therapy-content" 
          id="medication-therapy-header"
          sx={{ 
            backgroundColor: 'grey.200', 
            minHeight: '24px', 
            '&.Mui-expanded': { minHeight: '24px' },
            '& .MuiAccordionSummary-content': { 
              margin: '4px 0',
              '&.Mui-expanded': { margin: '4px 0' }
            }
          }}
        >
          <Typography sx={{fontWeight: 'medium', width: '100%', textAlign: 'center' }}>Medication Therapy</Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ borderTop: '1px solid', borderColor: 'divider', p: 1.5 }}>
          <Typography variant="body2" color="text.secondary">
            Medication Therapy content will go here...
          </Typography>
        </AccordionDetails>
      </Accordion>

      <Accordion expanded={true} sx={{ '&.MuiAccordion-root:before': { display: 'none' }, boxShadow: 'none', border: '1px solid', borderColor: 'divider', mb: 1.5 }}>
        <AccordionSummary 
          expandIcon={<ExpandMoreIcon />} 
          aria-controls="procedures-content" 
          id="procedures-header"
          sx={{ 
            backgroundColor: 'grey.200', 
            minHeight: '24px', 
            '&.Mui-expanded': { minHeight: '24px' },
            '& .MuiAccordionSummary-content': { 
              margin: '4px 0',
              '&.Mui-expanded': { margin: '4px 0' }
            }
          }}
        >
          <Typography sx={{fontWeight: 'medium', width: '100%', textAlign: 'center' }}>Procedures</Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ borderTop: '1px solid', borderColor: 'divider', p: 1.5 }}>
          <Typography variant="body2" color="text.secondary">
            Procedures content will go here...
          </Typography>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default EdTabContent; 
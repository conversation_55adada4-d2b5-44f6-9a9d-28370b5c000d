import React from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { 
  Accordion, 
  AccordionSummary, 
  AccordionDetails, 
  Typography, 
  Box, 
  Button, 
  Grid, 
  TextField, 
  FormControlLabel, 
  Checkbox,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import type { SxProps, Theme } from '@mui/material/styles';
import { EAndMLevelInline } from './EAndMLevelSelection';
import type { LevelItem } from '../../types/emLevels';
import { eAndMLevelsData } from '../../data/emLevelsData';
import { chartLogger } from '../../../../utils/logger';
import type { BOChartInfo } from '../../../../types/chartTypes';

interface EAndMLevelSectionProps {
  isExpanded: boolean;
  onAccordionChange: (event: React.SyntheticEvent, isExpanded: boolean) => void;
  levelCheckboxStates: Record<string, boolean>;
  handleEAndMSelectionChange: (itemId: string, checked: boolean) => void;
  textFieldSx: SxProps<Theme>;
  isEAndMLevelInlineOpen: boolean;
  handleToggleEAndMLevelInline: () => void;
  chartInfo: BOChartInfo;
  isLoading: boolean;
}

const EAndMLevelSection: React.FC<EAndMLevelSectionProps> = ({
  isExpanded,
  onAccordionChange,
  levelCheckboxStates,
  handleEAndMSelectionChange,
  textFieldSx,
  isEAndMLevelInlineOpen,
  handleToggleEAndMLevelInline,
  chartInfo,
  isLoading
}) => {
  const { control } = useFormContext(); // Get control from context

  // Helper function to count selected E&M items
  const getSelectedEAndMCount = () => {
    return Object.values(levelCheckboxStates).filter(Boolean).length;
  };

  // Use hard-coded data for level structure to match checkbox state IDs
  // This ensures chip display works correctly with form state
  const levelsData = React.useMemo(() => {
    chartLogger.debug('EAndMLevelSection - Using hard-coded data for level structure', {
      hardCodedLevelsCount: eAndMLevelsData.length,
      usingHardCodedData: true
    });

    // Use hard-coded data to ensure ID consistency with form state
    return eAndMLevelsData;
  }, []);

  // Helper function to group selected E&M items by level (ordered from highest to lowest)
  const getSelectedItemsByLevel = React.useMemo(() => {
    const selectedByLevel: { [levelTitle: string]: LevelItem[] } = {};
    
    // Debug logging
    chartLogger.debug('EAndMLevelSection - Computing selected items by level', {
      levelCheckboxStates,
      levelsDataLength: levelsData.length,
      checkboxStateKeys: Object.keys(levelCheckboxStates),
      checkedItems: Object.entries(levelCheckboxStates).filter(([, value]) => value)
    });
    
    // Process levels in reverse order (Level 5, 4, 3, 2, 1)
    const reversedLevels = [...levelsData].reverse();
    
    reversedLevels.forEach(level => {
      const selectedItems = level.items.filter(item => {
        const isSelected = levelCheckboxStates[item.id];
        chartLogger.debug('EAndMLevelSection - Checking item', {
          levelTitle: level.levelTitle,
          itemId: item.id,
          itemLabel: item.label,
          isSelected,
          checkboxState: levelCheckboxStates[item.id]
        });
        return isSelected;
      });
      
      if (selectedItems.length > 0) {
        selectedByLevel[level.levelTitle] = selectedItems;
        chartLogger.debug('EAndMLevelSection - Found selected items for level', {
          levelTitle: level.levelTitle,
          selectedItemsCount: selectedItems.length,
          selectedItems: selectedItems.map(item => ({ id: item.id, label: item.label }))
        });
      }
    });
    
    chartLogger.debug('EAndMLevelSection - Final selected items by level', {
      selectedByLevel,
      totalLevelsWithSelections: Object.keys(selectedByLevel).length,
      willShowChips: Object.keys(selectedByLevel).length > 0
    });
    
    return selectedByLevel;
  }, [levelCheckboxStates, levelsData]);

  const handleRemoveEAndMItem = (itemId: string) => {
    chartLogger.debug('Removing E&M item', { itemId, currentStates: levelCheckboxStates });
    handleEAndMSelectionChange(itemId, false);
  };

  return (
    <Accordion 
      expanded={isExpanded} 
      onChange={onAccordionChange} 
      sx={{ 
        '&.MuiAccordion-root:before': { display: 'none' }, 
        boxShadow: 'none', 
        border: '1px solid', 
        borderColor: 'divider', 
        mb: 1.5 
      }}
    >
      <AccordionSummary 
        expandIcon={<ExpandMoreIcon />} 
        aria-controls="eandm-level-content" 
        id="eandm-level-header"
        sx={{ 
          backgroundColor: 'grey.200', 
          minHeight: '24px', 
          '&.Mui-expanded': { minHeight: '24px' },
          '& .MuiAccordionSummary-content': { 
            margin: '4px 0',
            '&.Mui-expanded': { margin: '4px 0' }
          }
        }}
      >
        <Typography sx={{fontWeight: 'medium', width: '100%', textAlign: 'center' }}>E&M Level</Typography>
      </AccordionSummary>
      <AccordionDetails sx={{ borderTop: '1px solid', borderColor: 'divider', p: 1.5 }}>
        <Grid container spacing={1.5} alignItems="center" sx={{ mb: 1.5 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Controller
              name="dischargeStatus"
              control={control}
              render={({ field }) => (
                <TextField 
                  {...field}
                  label="Discharge Status" 
                  variant="outlined" 
                  size="small" 
                  fullWidth 
                  sx={textFieldSx} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Controller
              name="provider"
              control={control}
              render={({ field }) => (
                <TextField 
                  {...field}
                  label="Provider" 
                  variant="outlined" 
                  size="small" 
                  fullWidth 
                  sx={textFieldSx} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Controller
              name="traumaActivation"
              control={control}
              render={({ field }) => (
                <TextField 
                  {...field}
                  label="Trauma Activation" 
                  variant="outlined" 
                  size="small" 
                  fullWidth 
                  sx={textFieldSx} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Controller
              name="specialNoCharge"
              control={control}
              render={({ field }) => (
                <TextField 
                  {...field}
                  label="Special / No Charge" 
                  variant="outlined" 
                  size="small" 
                  fullWidth 
                  sx={textFieldSx} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Controller
              name="criticalCareMins"
              control={control}
              render={({ field }) => (
                <TextField 
                  {...field}
                  label="Critical Care Mins" 
                  variant="outlined" 
                  size="small" 
                  fullWidth 
                  sx={textFieldSx} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={4} md={2}>
            <Controller
              name="mod25"
              control={control}
              render={({ field }) => (
                <FormControlLabel 
                  control={<Checkbox {...field} checked={field.value} size="small" />} 
                  label="Mod 25" 
                  sx={{ 
                    width: '100%',
                    alignItems: 'center',
                    '& .MuiFormControlLabel-label': {
                      lineHeight: 1.2
                    }
                  }} 
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={8} md={4}>
            <Controller
              name="mod59"
              control={control}
              render={({ field }) => (
                <FormControlLabel 
                  control={<Checkbox {...field} checked={field.value} size="small" />} 
                  label="Mod 59 / XU / IV Contrast" 
                  sx={{ 
                    width: '100%',
                    alignItems: 'center',
                    '& .MuiFormControlLabel-label': {
                      lineHeight: 1.2
                    }
                  }} 
                />
              )}
            />
          </Grid>
          
          {/* Compact E&M Levels Toggle */}
          <Grid item xs={12} md={3} sx={{ display: 'flex', alignItems: 'center', justifyContent: { xs: 'center', md: 'flex-end' } }}>
            <Button 
              variant="text" 
              size="small"
              onClick={handleToggleEAndMLevelInline}
              sx={{ 
                textTransform: 'none',
                fontSize: '0.875rem',
                fontWeight: 500,
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.50'
                }
              }}
            >
              {isEAndMLevelInlineOpen ? 'Hide' : 'Select'} E&M Levels
              {getSelectedEAndMCount() > 0 && (
                <Chip 
                  label={getSelectedEAndMCount()} 
                  size="small" 
                  color="primary"
                  sx={{ 
                    ml: 1, 
                    height: '20px',
                    fontSize: '0.75rem',
                    '& .MuiChip-label': {
                      px: 1
                    }
                  }} 
                />
              )}
            </Button>
          </Grid>
        </Grid>

        {/* Inline E&M Level Selector */}
        {isEAndMLevelInlineOpen && (
          <EAndMLevelInline
            selections={levelCheckboxStates}
            onSelectionChange={handleEAndMSelectionChange}
            onClose={handleToggleEAndMLevelInline}
            chartInfo={chartInfo}
            isLoading={isLoading}
          />
        )}

        {/* Display selected E&M items grouped by level */}
        {Object.keys(getSelectedItemsByLevel).length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold' }}>
              Selected E&M Procedures:
            </Typography>
            {Object.entries(getSelectedItemsByLevel).map(([levelTitle, items]) => (
              <Card key={levelTitle} sx={{ mb: 2, border: '1px solid', borderColor: 'divider' }}>
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Typography variant="subtitle2" sx={{ mb: 1.5, fontWeight: 'bold', color: 'primary.main' }}>
                    {levelTitle} ({items.length} procedure{items.length !== 1 ? 's' : ''})
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {items.map((item) => (
                      <Chip
                        key={item.id}
                        label={item.label}
                        size="small"
                        variant="outlined"
                        onDelete={() => handleRemoveEAndMItem(item.id)}
                        sx={{ 
                          fontSize: '0.75rem',
                          height: '24px',
                          '& .MuiChip-deleteIcon': {
                            fontSize: '16px',
                            color: 'text.secondary',
                            '&:hover': {
                              color: 'error.main'
                            }
                          }
                        }}
                      />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            ))}
          </Box>
        )}
      </AccordionDetails>
    </Accordion>
  );
};

export default EAndMLevelSection;

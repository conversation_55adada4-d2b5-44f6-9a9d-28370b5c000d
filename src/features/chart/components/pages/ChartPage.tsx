/**
 * ChartPage Component
 * 
 * Purpose:
 * This component is the primary presentational view for an individual patient chart. It is
 * designed to work within the `ChartContainer`, which handles all data fetching, loading,
 * and error states.
 * 
 * Responsibilities:
 * 1.  Renders the main layout of the chart page, including the header, tabs, and content sections.
 * 2.  Subscribes to `useChartStore` to get the chart data (`chartInfo`) that has already
 *     been fetched by `ChartContainer`.
 * 3.  Manages the local UI state for all form fields (e.g., patient demographics, dates, notes).
 * 4.  Initializes form fields with data from `chartInfo` when it becomes available.
 * 5.  Tracks user edits to form fields to prevent fetched data from overwriting user input.
 * 6.  Parses `chartRedisplay` records from the chart data to initialize the E&M Level selections.
 * 7.  Manages the state and interactions of UI elements like accordions and tabs.
 * 8.  Delegates rendering and state management of specific sections to child components
 *     like `ChartHeader` and `EAndMLevelSection`.
 * 
 * Note:
 * This component does not perform any direct data fetching. It relies on `ChartContainer`
 * to manage the data lifecycle.
 */
import React from 'react';
import { useParams } from 'react-router-dom';
import { useForm, FormProvider } from 'react-hook-form';
import { 
  Container, 
  Box, 
  Tabs, 
  Tab, 
} from '@mui/material';
import ChartHeader from '../sections/ChartHeader';
import EdTabContent from '../tabs/EdTabContent';
import ObsTabContent from '../tabs/ObsTabContent';
import ProfeeTabContent from '../tabs/ProfeeTabContent';
// Store import removed - data now comes via props
import type { BOChartInfo } from '../../../../types/chartTypes';
import { chartLogger } from '../../../../utils/logger';
import { useChartFormData, type ChartFormValues } from '../../hooks/data/useChartFormData';
import { useChartUIState, useTabDirtyTracking } from '../../hooks/ui/useChartUIState';
import { useChartFormEffects } from '../../hooks/effects/useChartFormEffects';

// Form data type is now imported from the hook

/**
 * Inner component that uses dirty tracking within FormProvider context
 */
const ChartPageContent: React.FC<{
  textFieldSx: React.CSSProperties;
  selectedTab: number;
  handleTabChange: (event: React.SyntheticEvent, newValue: number) => void;
  isEAndMLevelInlineOpen: boolean;
  setIsEAndMLevelInlineOpen: React.Dispatch<React.SetStateAction<boolean>>;
  chartInfo: BOChartInfo;
  isLoading: boolean;
}> = ({
  textFieldSx,
  selectedTab,
  handleTabChange,
  isEAndMLevelInlineOpen,
  setIsEAndMLevelInlineOpen,
  chartInfo,
  isLoading,
}) => {
  // Use dirty tracking inside FormProvider context
  const {
    isEdTabDirty,
    isObsTabDirty,
    isProfeeTabDirty,
    isAnyTabDirty,
  } = useTabDirtyTracking();

  return (
    <>
      <Box
        data-testid="chart-page"
        component="header"
        sx={{
          position: 'sticky',
          top: 0,
          zIndex: theme => theme.zIndex.appBar - 1,
          backgroundColor: 'background.paper',
          borderBottom: theme => `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box sx={{ flexDirection: 'column', alignItems: 'stretch', pt: 0.75, pb: 0.75, gap: 0.5, px: 1.5 }}>
          <ChartHeader
            textFieldSx={textFieldSx}
            isAnyTabDirty={isAnyTabDirty}
          />
          <Box sx={{ width: '100%', borderBottom: 1, borderColor: 'divider', backgroundColor: 'background.paper' }}>
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              aria-label="chart sections tabs"
              sx={{
                minHeight: '36px',
                '& .MuiTab-root': {
                  minHeight: '36px',
                  padding: '6px 12px',
                  fontSize: '0.875rem'
                }
              }}
            >
              <Tab
                label={
                  <span>
                    ED
                    {isEdTabDirty && (
                      <span style={{ color: '#f44336', marginLeft: '4px' }}>●</span>
                    )}
                  </span>
                }
              />
              <Tab
                label={
                  <span>
                    Obs
                    {isObsTabDirty && (
                      <span style={{ color: '#f44336', marginLeft: '4px' }}>●</span>
                    )}
                  </span>
                }
              />
              <Tab
                label={
                  <span>
                    Profee
                    {isProfeeTabDirty && (
                      <span style={{ color: '#f44336', marginLeft: '4px' }}>●</span>
                    )}
                  </span>
                }
              />
            </Tabs>
          </Box>
        </Box>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: 'background.default'
        }}
      >
        <Container
          disableGutters
          maxWidth={false}
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
            overflowY: 'hidden'
          }}
        >

          {selectedTab === 0 && (
            <EdTabContent
              textFieldSx={textFieldSx}
              isEAndMLevelInlineOpen={isEAndMLevelInlineOpen}
              setIsEAndMLevelInlineOpen={setIsEAndMLevelInlineOpen}
              chartInfo={chartInfo}
              isLoading={isLoading}
            />
          )}
          {selectedTab === 1 && (
            <ObsTabContent
              textFieldSx={textFieldSx}
            />
          )}
          {selectedTab === 2 && (
            <ProfeeTabContent
              textFieldSx={textFieldSx}
            />
          )}
        </Container>
      </Box>
    </>
  );
};

interface ChartPageProps {
  chartInfo: BOChartInfo;
  isLoading: boolean;
}

const ChartPage: React.FC<ChartPageProps> = ({ chartInfo, isLoading: isChartLoading }) => {
  const { visitId: routeVisitId } = useParams<{ visitId: string }>();
  
  // Chart data and loading state are now received as props
  
  // Extract form data transformation logic
  const formData = useChartFormData(chartInfo, routeVisitId);
  
  // Extract UI state management
  const {
    selectedTab,
    handleTabChange,
    isEAndMLevelInlineOpen,
    setIsEAndMLevelInlineOpen,
  } = useChartUIState();
  
  const methods = useForm<ChartFormValues>({
    defaultValues: {
      visitId: '',
      medicalRecordNumber: '',
      lastName: '',
      firstName: '',
      dob: '',
      age: '',
      edDos: '',
      edStart: '',
      edEnd: '',
      obsStart: '',
      obsEnd: '',
      isEd: false,
      isObs: false,
      treatmentArea: '',
      edChartStatus: '',
      note: '',
      dischargeStatus: '',
      provider: '',
      traumaActivation: '',
      specialNoCharge: '',
      criticalCareMins: '',
      mod25: false,
      mod59: false,
      levelCheckboxStates: {},
    }
  });
  const { reset, formState: { isDirty }, setValue } = methods;

  // Performance tracking - log renders with reason
  React.useEffect(() => {
    chartLogger.perf('ChartPage rendered', { routeVisitId });
  });

  // Log chart state changes for debugging
  React.useEffect(() => {
    chartLogger.debug('Zustand store state updated', {
      isLoading: isChartLoading,
      hasData: !!chartInfo,
    });
  }, [isChartLoading, chartInfo]);

  // Extract form effects management
  useChartFormEffects(
    formData,
    isDirty,
    reset,
    setValue,
    routeVisitId,
    setIsEAndMLevelInlineOpen
  );

  // Form data transformation and effects are now handled by custom hooks

  const textFieldSx = { backgroundColor: 'background.paper', borderRadius: 1, width: '100%' };

  // This effect is for debugging and can be removed or simplified.
  // It demonstrates how to react to changes in the selected facility.
  React.useEffect(() => {
    if (chartInfo) {
      chartLogger.debug('Chart info loaded', { visitID: chartInfo.visitID });
    }
  }, [chartInfo]);

  // Loading and error states are now handled by ChartContainer.
  // This component will only render when data is successfully loaded.

  return (
    <FormProvider {...methods}>
      <ChartPageContent
        textFieldSx={textFieldSx}
        selectedTab={selectedTab}
        handleTabChange={handleTabChange}
        isEAndMLevelInlineOpen={isEAndMLevelInlineOpen}
        setIsEAndMLevelInlineOpen={setIsEAndMLevelInlineOpen}
        chartInfo={chartInfo}
        isLoading={isChartLoading}
      />
    </FormProvider>
  );
};

export default ChartPage;

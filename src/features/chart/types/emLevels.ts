/**
 * E&M Level Types
 * 
 * Type definitions for Emergency & Medicine (E&M) level functionality.
 * These types are used across chart components for E&M level selection,
 * display, and data management.
 */

import type { BOChartInfo } from '../../../types/chartTypes';

/**
 * Represents a single E&M level item/procedure
 */
export interface LevelItem {
  /** Unique identifier for the level item */
  id: string;
  /** Human-readable label for the level item */
  label: string;
}

/**
 * Represents a complete E&M level with its associated items/procedures
 */
export interface EAndMLevel {
  /** Title of the E&M level (e.g., "Level 1", "Level 2") */
  levelTitle: string;
  /** Array of procedures/items available for this level */
  items: LevelItem[];
}

/**
 * Props interface for the EAndMLevelInline component
 */
export interface EAndMLevelInlineProps {
  /** Current selection state for all level items */
  selections: Record<string, boolean>;
  /** Callback when a level item selection changes */
  onSelectionChange: (itemId: string, checked: boolean) => void;
  /** Optional callback when the inline component should be closed */
  onClose?: () => void;
  /** Force use of hard-coded data (for testing purposes) */
  useHardcodedData?: boolean;
  /** Chart information object */
  chartInfo?: BOChartInfo | null;
  /** Whether chart data is loading */
  isLoading?: boolean;
}

/**
 * Re-export RedisplayRecord type for components that need it
 * This maintains backward compatibility for existing imports
 */
export type { RedisplayRecord } from '../../../utils/emLevelMapping';